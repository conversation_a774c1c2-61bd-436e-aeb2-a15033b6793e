<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <title>پروفایل {{ customer.first_name }} {{ customer.last_name }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="container mt-4">

    <h3>🧑‍💼 پروفایل مشتری: {{ customer.first_name }} {{ customer.last_name }}</h3>
    <p>📞 {{ customer.phone }} | 🏠 {{ customer.address }}</p>
    <p><strong>بدهی کل: {{ customer.balance }} تومان</strong></p>

    <hr>

    <!-- فرم ثبت خرید -->
    <h5>➕ ثبت خرید جدید</h5>
    <form method="POST" action="{{ url_for('add_purchase', name=name) }}" class="row g-2 mb-3">
        <div class="col">
            <input type="text" name="item" class="form-control" placeholder="نام کالا">
        </div>
        <div class="col">
            <input type="number" name="price" class="form-control" placeholder="قیمت">
        </div>
        <div class="col">
            <button type="submit" class="btn btn-primary">ثبت</button>
        </div>
    </form>

    <!-- لیست خریدها -->
    <h5>🛒 خریدهای ثبت‌شده</h5>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ردیف</th>
                <th>کالا</th>
                <th>قیمت</th>
                <th>تاریخ (شمسی)</th>
            </tr>
        </thead>
        <tbody>
            {% for p in customer.purchases %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ p.item }}</td>
                <td>{{ p.price }} تومان</td>
                <td>{{ p.date }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="4" class="text-muted">هیچ خریدی ثبت نشده</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <a href="{{ url_for('index') }}" class="btn btn-secondary mt-3">⬅ بازگشت به لیست مشتری‌ها</a>

</body>
</html>
