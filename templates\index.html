<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <title>دفتر حساب مشتری‌ها</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body class="container mt-4">

    <h2 class="mb-4">📒 دفتر حساب مشتری‌ها</h2>

    <!-- فرم افزودن مشتری -->
    <form method="POST" action="/add" class="mb-4">
        <div class="row g-2">
            <div class="col">
                <input type="text" name="name" class="form-control" placeholder="نام مشتری" required>
            </div>
            <div class="col">
                <input type="text" name="phone" class="form-control" placeholder="شماره تماس">
            </div>
            <div class="col">
                <input type="text" name="address" class="form-control" placeholder="آدرس">
            </div>
            <div class="col">
                <button type="submit" class="btn btn-success">➕ افزودن</button>
            </div>
        </div>
    </form>

    <!-- فرم جستجو -->
    <form method="GET" action="/search" class="mb-3">
        <input type="text" name="q" class="form-control" placeholder="جستجو بر اساس نام یا شماره">
    </form>

    <!-- لیست مشتری‌ها -->
    <ul class="list-group">
        {% for i, (name, info) in enumerate(customers.items(), start=1) %}
        <li class="list-group-item">
            <strong>{{ i }}. {{ name }}</strong> - {{ info.phone }} - {{ info.address }}
            <span class="badge bg-warning">بدهی: {{ info.balance }} تومان</span>
        </li>
        {% endfor %}
    </ul>

</body>
</html>
