from flask import Flask, render_template, request, redirect
import json
import os

app = Flask(__name__)
DATA_FILE = "customers.json"

# --- توابع ذخیره/لود داده ---
def load_data():
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {}

def save_data(data):
    with open(DATA_FILE, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

# --- صفحه اصلی: نمایش لیست مشتری‌ها ---
@app.route("/")
def index():
    customers = load_data()
    return render_template("index.html", customers=customers)

# --- افزودن مشتری جدید ---
@app.route("/add", methods=["POST"])
def add_customer():
    data = load_data()
    name = request.form["name"]
    phone = request.form["phone"]
    address = request.form["address"]

    if name not in data:
        data[name] = {
            "phone": phone,
            "address": address,
            "balance": 0,
            "purchases": []
        }
        save_data(data)

    return redirect("/")

# --- جستجوی مشتری ---
@app.route("/search", methods=["GET"])
def search():
    query = request.args.get("q", "")
    customers = load_data()
    result = {k: v for k, v in customers.items() if query in k or query in v.get("phone", "")}
    return render_template("index.html", customers=result)

if __name__ == "__main__":
    app.run(debug=True)
