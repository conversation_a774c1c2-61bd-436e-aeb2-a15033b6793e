from flask import Flask, render_template, request, redirect, url_for
import json
import os
import jdatetime
from datetime import datetime

app = Flask(__name__)
DATA_FILE = "customers.json"

# --- توابع ذخیره/لود داده ---
def load_data():
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {}

def save_data(data):
    with open(DATA_FILE, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

# --- صفحه اصلی: نمایش لیست مشتری‌ها ---
@app.route("/")
def index():
    customers = load_data()
    return render_template("index.html", customers=customers)

# --- افزودن مشتری جدید ---
@app.route("/add", methods=["POST"])
def add_customer():
    data = load_data()

    first_name = request.form.get("first_name", "").strip() or "نام‌ثبت‌نشده"
    last_name = request.form.get("last_name", "").strip() or "نام‌خانوادگی‌ثبت‌نشده"
    phone = request.form.get("phone", "").strip() or "0"
    address = request.form.get("address", "").strip() or "0"

    key = f"{first_name} {last_name}"

    if key not in data:
        data[key] = {
            "first_name": first_name,
            "last_name": last_name,
            "phone": phone,
            "address": address,
            "balance": 0,
            "purchases": []
        }
        save_data(data)

    return redirect("/")

# --- صفحه اختصاصی هر مشتری ---
@app.route("/customer/<name>")
def customer_page(name):
    data = load_data()
    customer = data.get(name)
    if not customer:
        return "مشتری یافت نشد", 404
    return render_template("customer.html", name=name, customer=customer)

# --- ثبت خرید برای مشتری ---
@app.route("/customer/<name>/add_purchase", methods=["POST"])
def add_purchase(name):
    data = load_data()
    customer = data.get(name)
    if not customer:
        return "مشتری یافت نشد", 404

    item = request.form.get("item", "").strip() or "کالا"
    price = int(request.form.get("price", "0") or 0)

    # تاریخ شمسی
    now = jdatetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    customer["purchases"].append({
        "item": item,
        "price": price,
        "date": now
    })

    # اضافه به بدهی
    customer["balance"] += price

    data[name] = customer
    save_data(data)

    return redirect(url_for("customer_page", name=name))

# --- جستجوی مشتری ---
@app.route("/search", methods=["GET"])
def search():
    query = request.args.get("q", "").strip()
    customers = load_data()

    if not query:
        return render_template("index.html", customers=customers)

    result = {
        k: v for k, v in customers.items()
        if query in k or query in v.get("phone", "")
    }
    return render_template("index.html", customers=result)

if __name__ == "__main__":
    app.run(debug=True)
